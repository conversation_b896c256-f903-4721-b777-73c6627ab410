# Enhanced Prosodic Analysis for Chinese Linguistics Research

## Overview

This repository contains an integrated R script for analyzing prosodic patterns in the Chinese sentence "每只猫吃着一盆猫粮" (mei-zhi mao chi-zhe yi-pen maoliang) under three different stress conditions. The visualization follows academic standards for linguistics journal publication.

## Experimental Conditions

1. **Ex1: Neutral Prosody** - No stress/neutral prosodic pattern
2. **Ex2: Subject Stress** - Stress on the subject "每只猫" (mei-zhi mao)
3. **Ex3: Object Stress** - Stress on the object "一盆猫粮" (yi-pen maoliang)

## Academic Standards Implemented

### Visual Design Features
- ✅ **Complete border frames** around each plot
- ✅ **Vibrant, professional color schemes** suitable for academic publication
- ✅ **Syllable labels positioned below x-axis** to avoid overlap with prosodic lines
- ✅ **Semi-transparent shaded regions** for pitch accents instead of dashed lines
- ✅ **Morandi-style background shading** for stressed regions (muted, desaturated colors)
- ✅ **High-resolution output** (300 DPI) suitable for publication

### Academic Conventions
- Follows established conventions for prosodic analysis visualization in linguistics journals
- Uses standardized axis labeling: "Fundamental Frequency (Hz)" for y-axis
- Implements proper syllable segmentation and labeling
- Provides both individual plots and comparative multi-panel figures

## Files Generated

### Individual Plots
- `Prosody_Ex1_Neutral.png/.pdf` - Neutral prosody condition
- `Prosody_Ex2_Subject_Stress.png/.pdf` - Subject stress condition  
- `Prosody_Ex3_Object_Stress.png/.pdf` - Object stress condition

### Comparative Analysis
- `Prosody_Comparative_Analysis.png/.pdf` - Multi-panel figure for comparative analysis

## Key Features

### Color Scheme
- **Primary contour**: Deep blue (#2E86AB) for main pitch line
- **Data points**: Burgundy (#A23B72) for individual measurements
- **Stress highlighting**: 
  - Subject stress: Warm beige (#D4C5B9) background
  - Object stress: Cool blue-gray (#B8C5D1) background
- **Pitch accents**: Semi-transparent red regions (#C73E1D)

### Statistical Analysis
The script automatically generates:
- Summary statistics for each experimental condition
- Stress-specific regional analysis
- Comparative F0 measurements between neutral and stressed conditions

## Results Summary

### Overall F0 Statistics
- **Neutral**: Mean = 293.85 Hz (SD = 33.35)
- **Subject Stress**: Mean = 313.19 Hz (SD = 27.48)
- **Object Stress**: Mean = 321.19 Hz (SD = 52.58)

### Regional Analysis
- **Subject region stress effect**: +58 Hz increase in mean F0
- **Object region stress effect**: +60 Hz increase in mean F0

## Usage Instructions

### Prerequisites
```r
# Required R packages
library(readxl)
library(ggplot2)
library(cowplot)
library(ggpubr)
library(scales)
library(RColorBrewer)
library(viridis)
library(grid)
library(gridExtra)
```

### Running the Analysis
1. Ensure your working directory contains the `Ex1`, `Ex2`, and `Ex3` folders with respective data files
2. Run the integrated script:
```r
source("integrated_prosody_analysis.R")
```

### Data Structure
Each experiment folder should contain:
- `normf1.xlsx` (Ex1 data)
- `normf2.xlsx` (Ex2 data)  
- `normf3.xlsx` (Ex3 data)

Data format: 4 columns (`File/Normtime`, `f0`, `point`, `f00`)
- `point`: Time points (0.1 to 9.0)
- `f00`: Fundamental frequency values in Hz

## Academic Applications

This visualization system is designed for:
- **Linguistics journal submissions** (Journal of Phonetics, Language and Speech, etc.)
- **Conference presentations** in phonetics and prosody research
- **Comparative prosodic analysis** across different stress conditions
- **Chinese linguistics research** focusing on prosodic prominence

## Technical Specifications

- **Resolution**: 300 DPI for print quality
- **Format**: Both PNG (presentations) and PDF (publication) versions
- **Dimensions**: 10×6 inches (individual), 12×16 inches (multi-panel)
- **Color space**: RGB for digital, suitable for CMYK conversion

## Citation

When using this visualization system in academic work, please cite appropriately and ensure compliance with your institution's research standards.

## Notes

- Chinese character encoding warnings are normal and do not affect the visualization quality
- The script automatically handles data loading and error checking
- All visualizations maintain scientific accuracy while enhancing aesthetic appeal
- Morandi color palette provides subtle, professional highlighting without overwhelming the data
