# ===============================================================================
# Integrated Prosodic Analysis for Chinese Sentence: "mei-zhi mao chi-zhe yipen maoliang"
# Academic Standard Visualization for Linguistics Research
# 
# Three experimental conditions:
# Ex1: Neutral prosody (no stress)
# Ex2: Subject stress ("mei-zhi mao")  
# Ex3: Object stress ("yi-pen maoliang")
# ===============================================================================

# Load required libraries
library(readxl)
library(ggplot2)
library(cowplot)
library(ggpubr)
library(scales)
library(RColorBrewer)
library(viridis)
library(grid)
library(gridExtra)

# Set working directory (current directory should contain Ex1, Ex2, Ex3 folders)
# setwd(dirname(rstudioapi::getActiveDocumentContext()$path))  # Only works in RStudio
# Working directory should already be set to the project root

# ===============================================================================
# ACADEMIC VISUALIZATION STANDARDS
# ===============================================================================

# Define academic color palette (vibrant but professional)
academic_colors <- list(
  primary = "#2E86AB",      # Deep blue for main contour
  secondary = "#A23B72",    # Burgundy for data points
  accent1 = "#F18F01",      # Orange for highlights
  accent2 = "#C73E1D",      # Red for pitch accents
  neutral = "#708090",      # Slate gray for neutral elements
  background = "#F8F9FA"    # Light background
)

# Morandi-style colors for stress highlighting (muted, desaturated)
morandi_colors <- list(
  subject_stress = "#D4C5B9",    # Warm beige
  object_stress = "#B8C5D1",     # Cool blue-gray
  neutral_bg = "#F5F5F5"         # Very light gray
)

# Define syllable information
syllables <- c("mei", "zhi", "mao", "chi", "zhe", "yi", "pen", "mao", "liang")
syllable_positions <- seq(0.5, 8.5, 1)

# Stress regions definition
subject_stress_region <- c(0, 3)    # "mei-zhi mao" (syllables 1-3)
object_stress_region <- c(5, 9)     # "yi-pen maoliang" (syllables 6-9)

# ===============================================================================
# DATA LOADING FUNCTION
# ===============================================================================

load_experiment_data <- function(exp_num) {
  file_path <- file.path(paste0("Ex", exp_num), paste0("normf", exp_num, ".xlsx"))
  if (!file.exists(file_path)) {
    stop(paste("Data file not found:", file_path))
  }
  data <- read_excel(file_path)
  data$point <- as.numeric(data$point)
  data$experiment <- paste0("Ex", exp_num)
  return(data)
}

# ===============================================================================
# ENHANCED PLOTTING FUNCTION
# ===============================================================================

create_prosody_plot <- function(data, exp_num, title_suffix = "") {
  
  # Determine stress condition and background shading
  stress_condition <- switch(exp_num,
    "1" = "neutral",
    "2" = "subject",
    "3" = "object"
  )
  
  # Base plot with academic styling
  p <- ggplot(data, aes(x = point, y = f00)) +
    
    # Add background shading for stressed regions (Morandi style)
    {if (stress_condition == "subject") {
      annotate("rect", 
               xmin = subject_stress_region[1], xmax = subject_stress_region[2],
               ymin = -Inf, ymax = Inf,
               fill = morandi_colors$subject_stress, alpha = 0.3)
    }} +
    
    {if (stress_condition == "object") {
      annotate("rect", 
               xmin = object_stress_region[1], xmax = object_stress_region[2],
               ymin = -Inf, ymax = Inf,
               fill = morandi_colors$object_stress, alpha = 0.3)
    }} +
    
    # Main pitch contour line (vibrant color)
    geom_line(linewidth = 2.5, color = academic_colors$primary, alpha = 0.9) +
    
    # Data points
    geom_point(size = 3.5, color = academic_colors$secondary, alpha = 0.8) +
    
    # Semi-transparent shaded regions for pitch accents (instead of dashed lines)
    {if (stress_condition == "subject") {
      annotate("rect", 
               xmin = subject_stress_region[1], xmax = subject_stress_region[2],
               ymin = max(data$f00) * 0.95, ymax = max(data$f00) * 1.02,
               fill = academic_colors$accent2, alpha = 0.6)
    }} +
    
    {if (stress_condition == "object") {
      annotate("rect", 
               xmin = object_stress_region[1], xmax = object_stress_region[2],
               ymin = max(data$f00) * 0.95, ymax = max(data$f00) * 1.02,
               fill = academic_colors$accent2, alpha = 0.6)
    }} +
    
    # Academic theme with complete borders
    theme_bw(base_size = 14) +
    theme(
      # Complete border frame
      panel.border = element_rect(color = "black", fill = NA, linewidth = 1.2),
      
      # Background
      panel.background = element_rect(fill = "white", color = NA),
      plot.background = element_rect(fill = "white", color = NA),
      
      # Grid lines (minimal, academic style)
      panel.grid.major.y = element_line(color = "grey90", linewidth = 0.5, linetype = "dotted"),
      panel.grid.minor = element_blank(),
      panel.grid.major.x = element_blank(),
      
      # Axis styling
      axis.line = element_line(color = "black", linewidth = 0.8),
      axis.ticks = element_line(color = "black", linewidth = 0.6),
      axis.text.x = element_blank(),  # Remove x-axis labels (syllables go below)
      axis.text.y = element_text(color = "black", size = 12, face = "plain"),
      axis.title = element_text(color = "black", size = 14, face = "bold"),
      
      # Title styling
      plot.title = element_text(hjust = 0.5, size = 16, face = "bold", color = "black"),
      plot.subtitle = element_text(hjust = 0.5, size = 12, color = "grey30"),
      
      # Margins
      plot.margin = margin(20, 20, 40, 20)  # Extra bottom margin for syllable labels
    ) +
    
    # Axis configuration
    scale_x_continuous(
      limits = c(0, 9),
      breaks = seq(0, 9, 1),
      expand = c(0.02, 0.02)
    ) +
    scale_y_continuous(
      breaks = pretty_breaks(n = 6),
      expand = c(0.05, 0.05)
    ) +
    
    # Labels
    labs(
      x = "",  # Will add syllable labels separately
      y = "Fundamental Frequency (Hz)",
      title = paste0("Experiment ", exp_num, ": ", title_suffix),
      subtitle = "每只猫吃着一盆猫粮 (mei-zhi mao chi-zhe yi-pen maoliang)"
    )
  
  # Add syllable labels below x-axis (positioned to avoid overlap)
  y_label_position <- min(data$f00) - (max(data$f00) - min(data$f00)) * 0.15
  
  for (i in 1:length(syllables)) {
    p <- p + annotate("text",
                     x = syllable_positions[i],
                     y = y_label_position,
                     label = syllables[i],
                     vjust = 1,
                     size = 4.5,
                     color = "black",
                     fontface = "bold")
  }
  
  # Add syllable separator lines (subtle)
  p <- p + geom_vline(xintercept = seq(1, 8, 1),
                     color = "grey85",
                     linetype = "dotted",
                     alpha = 0.7,
                     linewidth = 0.3)
  
  return(p)
}

# ===============================================================================
# MAIN EXECUTION
# ===============================================================================

# Load data for all experiments
cat("Loading experimental data...\n")
data_ex1 <- load_experiment_data(1)
data_ex2 <- load_experiment_data(2)
data_ex3 <- load_experiment_data(3)

# Create individual plots
cat("Creating individual prosodic diagrams...\n")
plot_ex1 <- create_prosody_plot(data_ex1, "1", "Neutral Prosody")
plot_ex2 <- create_prosody_plot(data_ex2, "2", "Subject Stress")
plot_ex3 <- create_prosody_plot(data_ex3, "3", "Object Stress")

# Display individual plots
print(plot_ex1)
print(plot_ex2)
print(plot_ex3)

# ===============================================================================
# SAVE INDIVIDUAL HIGH-QUALITY PLOTS
# ===============================================================================

cat("Saving individual plots...\n")

# Save individual plots (PNG format for presentations)
ggsave("Prosody_Ex1_Neutral.png", plot_ex1, 
       width = 10, height = 6, units = "in", dpi = 300, bg = "white")
ggsave("Prosody_Ex2_Subject_Stress.png", plot_ex2, 
       width = 10, height = 6, units = "in", dpi = 300, bg = "white")
ggsave("Prosody_Ex3_Object_Stress.png", plot_ex3, 
       width = 10, height = 6, units = "in", dpi = 300, bg = "white")

# Save individual plots (PDF format for academic publication)
ggsave("Prosody_Ex1_Neutral.pdf", plot_ex1, 
       width = 10, height = 6, units = "in", device = "pdf")
ggsave("Prosody_Ex2_Subject_Stress.pdf", plot_ex2, 
       width = 10, height = 6, units = "in", device = "pdf")
ggsave("Prosody_Ex3_Object_Stress.pdf", plot_ex3, 
       width = 10, height = 6, units = "in", device = "pdf")

# ===============================================================================
# CREATE MULTI-PANEL COMPARATIVE FIGURE
# ===============================================================================

cat("Creating multi-panel comparative figure...\n")

# Create a combined multi-panel figure for academic publication
# Remove individual titles for cleaner multi-panel appearance
plot_ex1_clean <- plot_ex1 +
  labs(title = "A. Neutral Prosody", subtitle = NULL) +
  theme(plot.title = element_text(size = 14, face = "bold", hjust = 0))

plot_ex2_clean <- plot_ex2 +
  labs(title = "B. Subject Stress", subtitle = NULL) +
  theme(plot.title = element_text(size = 14, face = "bold", hjust = 0))

plot_ex3_clean <- plot_ex3 +
  labs(title = "C. Object Stress", subtitle = NULL) +
  theme(plot.title = element_text(size = 14, face = "bold", hjust = 0))

# Arrange plots in a single column for easy comparison
multi_panel <- plot_grid(
  plot_ex1_clean,
  plot_ex2_clean,
  plot_ex3_clean,
  ncol = 1,
  align = "v",
  axis = "lr",
  labels = NULL,
  rel_heights = c(1, 1, 1)
)

# Add overall title and subtitle
title_grob <- textGrob(
  "Prosodic Analysis of Chinese Sentence under Different Stress Conditions",
  gp = gpar(fontsize = 18, fontface = "bold")
)

subtitle_grob <- textGrob(
  "每只猫吃着一盆猫粮 (mei-zhi mao chi-zhe yi-pen maoliang)",
  gp = gpar(fontsize = 14, fontface = "italic", col = "grey30")
)

# Combine title, subtitle, and plots
final_figure <- grid.arrange(
  title_grob,
  subtitle_grob,
  multi_panel,
  heights = c(0.08, 0.05, 0.87),
  ncol = 1
)

# Display the multi-panel figure
grid.draw(final_figure)

# Save multi-panel figure
cat("Saving multi-panel comparative figure...\n")

# PNG version for presentations
ggsave("Prosody_Comparative_Analysis.png", final_figure,
       width = 12, height = 16, units = "in", dpi = 300, bg = "white")

# PDF version for academic publication
ggsave("Prosody_Comparative_Analysis.pdf", final_figure,
       width = 12, height = 16, units = "in", device = "pdf")

# ===============================================================================
# CREATE SUMMARY STATISTICS AND ANALYSIS
# ===============================================================================

cat("Generating summary statistics...\n")

# Combine all data for comparative analysis
all_data <- rbind(
  data.frame(data_ex1, condition = "Neutral"),
  data.frame(data_ex2, condition = "Subject_Stress"),
  data.frame(data_ex3, condition = "Object_Stress")
)

# Calculate summary statistics
summary_stats <- aggregate(f00 ~ condition, data = all_data,
                          FUN = function(x) c(
                            mean = mean(x),
                            sd = sd(x),
                            min = min(x),
                            max = max(x),
                            range = max(x) - min(x)
                          ))

# Print summary statistics
cat("\n=== PROSODIC ANALYSIS SUMMARY ===\n")
print(summary_stats)

# Calculate stress-specific statistics
cat("\n=== STRESS REGION ANALYSIS ===\n")

# Subject stress region analysis (syllables 1-3: points 0-3)
subject_region_ex2 <- data_ex2[data_ex2$point >= 0 & data_ex2$point <= 3, ]
subject_region_neutral <- data_ex1[data_ex1$point >= 0 & data_ex1$point <= 3, ]

cat("Subject region (mei-zhi mao):\n")
cat("  Neutral condition - Mean F0:", round(mean(subject_region_neutral$f00), 2), "Hz\n")
cat("  Subject stress - Mean F0:", round(mean(subject_region_ex2$f00), 2), "Hz\n")
cat("  Difference:", round(mean(subject_region_ex2$f00) - mean(subject_region_neutral$f00), 2), "Hz\n\n")

# Object stress region analysis (syllables 6-9: points 5-9)
object_region_ex3 <- data_ex3[data_ex3$point >= 5 & data_ex3$point <= 9, ]
object_region_neutral <- data_ex1[data_ex1$point >= 5 & data_ex1$point <= 9, ]

cat("Object region (yi-pen maoliang):\n")
cat("  Neutral condition - Mean F0:", round(mean(object_region_neutral$f00), 2), "Hz\n")
cat("  Object stress - Mean F0:", round(mean(object_region_ex3$f00), 2), "Hz\n")
cat("  Difference:", round(mean(object_region_ex3$f00) - mean(object_region_neutral$f00), 2), "Hz\n\n")

# ===============================================================================
# FINAL OUTPUT SUMMARY
# ===============================================================================

cat("=== PROSODIC ANALYSIS COMPLETE ===\n")
cat("Files created:\n")
cat("Individual plots:\n")
cat("- Prosody_Ex1_Neutral.png/.pdf\n")
cat("- Prosody_Ex2_Subject_Stress.png/.pdf\n")
cat("- Prosody_Ex3_Object_Stress.png/.pdf\n")
cat("Multi-panel comparative figure:\n")
cat("- Prosody_Comparative_Analysis.png/.pdf\n")
cat("\nAll visualizations follow academic standards for linguistics research.\n")
cat("Features implemented:\n")
cat("✓ Complete border frames\n")
cat("✓ Vibrant, professional color schemes\n")
cat("✓ Syllable labels positioned below x-axis\n")
cat("✓ Semi-transparent shaded regions for pitch accents\n")
cat("✓ Morandi-style background shading for stressed regions\n")
cat("✓ High-resolution output suitable for publication\n")
